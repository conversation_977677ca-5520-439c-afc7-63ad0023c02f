# iOS Setup Guide for FHIR Mobile MVP

This comprehensive guide will help you set up, build, and run the iOS version of the FHIR Mobile MVP application, including all necessary configurations for medical device integrations (particularly iHealth SDK).

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Development Environment Setup](#development-environment-setup)
3. [Project Configuration](#project-configuration)
4. [iHealth SDK Configuration](#ihealth-sdk-configuration)
5. [Building and Running](#building-and-running)
6. [Troubleshooting](#troubleshooting)
7. [Advanced Configuration](#advanced-configuration)

## Prerequisites

### System Requirements

- **macOS**: macOS 12.0 (Monterey) or later
- **Xcode**: Version 14.0 or later
- **iOS Deployment Target**: iOS 12.0 or later
- **Node.js**: Version 18.0 or later
- **npm/yarn**: Latest stable version

### Apple Developer Account

- Apple Developer Account (free or paid)
- For device testing and App Store distribution, a paid account ($99/year) is required

## Development Environment Setup

### 1. Install Xcode

```bash
# Install Xcode from the Mac App Store or Apple Developer Portal
# After installation, install command line tools:
xcode-select --install
```

### 2. Install Node.js and Package Manager

```bash
# Using Homebrew (recommended)
brew install node
brew install yarn  # Optional, but recommended

# Verify installation
node --version
npm --version
```

### 3. Install Expo CLI and Development Tools

```bash
# Install Expo CLI globally
npm install -g @expo/cli

# Install iOS Simulator (if not already installed with Xcode)
# This is included with Xcode installation
```

### 4. Install CocoaPods

```bash
# Install CocoaPods for iOS dependency management
sudo gem install cocoapods

# Verify installation
pod --version
```

## Project Configuration

### 1. Clone and Setup Project

```bash
# Navigate to your project directory
cd /path/to/fhir-mobile-mvp

# Install dependencies
npm install
# or
yarn install
```

### 2. iOS-Specific Configuration

The project includes an iOS configuration plugin (`plugins/withIOSConfiguration.js`) that automatically handles:

- iOS permissions for Bluetooth, Location, Camera, and HealthKit
- Background modes for continuous health monitoring
- iHealth SDK license file management
- App Transport Security settings
- iOS entitlements configuration

### 3. Configure app.json

Ensure your `app.json` includes the iOS configuration plugin:

```json
{
  "expo": {
    "plugins": [
      // ... other plugins
      "./plugins/withIOSConfiguration"
    ]
  }
}
```

### 4. Install iOS Dependencies

```bash
# Navigate to iOS directory and install pods
cd ios
pod install
cd ..
```

## iHealth SDK Configuration

### 1. Obtain iHealth License

1. Create a developer account at [dev.ihealthlabs.com](https://dev.ihealthlabs.com)
2. Register your iOS app with bundle identifier: `com.anonymous.fhirmobilemvp`
3. Download the iOS license file (`.pem` format)

### 2. License File Placement

Place your iHealth iOS license file in one of these locations:

```
# Option 1: Project root (recommended)
./com_anonymous_fhirmobilemvp_ios.pem

# Option 2: iOS app bundle directory
./ios/fhirmobilemvp/com_anonymous_fhirmobilemvp_ios.pem

# Option 3: Generic license name
./license.pem
```

The iOS configuration plugin will automatically detect and copy the license file to the correct location.

### 3. Verify iHealth SDK Integration

The project uses `@ihealth/ihealthlibrary-react-native` version 1.8.0. Verify it's installed:

```bash
npm list @ihealth/ihealthlibrary-react-native
```

## Building and Running

### 1. Development Build (Simulator)

```bash
# Start the development server
npm start
# or
yarn start

# In another terminal, run on iOS simulator
npm run ios
# or
yarn ios
```

### 2. Development Build (Physical Device)

```bash
# Run on connected iOS device
npx expo run:ios --device

# Or specify a specific device
npx expo run:ios --device "Your iPhone Name"
```

### 3. Production Build

```bash
# Create a production build
npx expo build:ios

# Or using EAS Build (recommended)
npx eas build --platform ios
```

## Troubleshooting

### Common Issues and Solutions

#### 1. CocoaPods Installation Issues

```bash
# If you encounter permission issues
sudo gem install cocoapods

# If you have Ruby version conflicts
brew install ruby
gem install cocoapods

# Clear CocoaPods cache
pod cache clean --all
cd ios && pod deintegrate && pod install
```

#### 2. Xcode Build Errors

**Error: "No such module 'ExpoModulesCore'"**
```bash
cd ios
pod install
```

**Error: Code signing issues**
1. Open `ios/fhirmobilemvp.xcworkspace` in Xcode
2. Select your project in the navigator
3. Go to "Signing & Capabilities"
4. Select your development team
5. Ensure bundle identifier matches your Apple Developer account

#### 3. iHealth SDK Issues

**Error: "iHealth license not found"**
- Ensure license file is in the correct location
- Check file name matches expected format
- Verify file permissions are readable

**Error: "iHealth authentication failed"**
- Verify license file is valid and not corrupted
- Ensure bundle identifier matches the license
- Check that you're using the correct iOS license (not Android)

#### 4. Bluetooth Permission Issues

**Error: "Bluetooth permission denied"**
- Ensure Info.plist contains proper usage descriptions
- Check that the iOS configuration plugin is applied
- Verify app has requested permissions at runtime

#### 5. Build Performance Issues

```bash
# Clear all caches
npx expo start --clear

# Clear iOS build cache
cd ios
rm -rf build/
pod cache clean --all
pod install

# Clear npm/yarn cache
npm cache clean --force
# or
yarn cache clean
```

### Device-Specific Troubleshooting

#### Physical Device Testing

1. **Enable Developer Mode** (iOS 16+):
   - Settings > Privacy & Security > Developer Mode > Enable

2. **Trust Developer Certificate**:
   - Settings > General > VPN & Device Management > Trust your certificate

3. **iHealth Device Connection**:
   - Ensure iHealth MyVitals app is closed
   - iHealth devices can only connect to one app at a time
   - Reset Bluetooth if connection fails

## Advanced Configuration

### Custom iOS Build Settings

To modify advanced iOS build settings, you can extend the `withIOSConfiguration` plugin:

```javascript
// In plugins/withIOSConfiguration.js
const withCustomBuildSettings = (config) => {
  return withDangerousMod(config, [
    "ios",
    async (config) => {
      // Add custom Xcode project modifications here
      return config;
    },
  ]);
};
```

### HealthKit Integration

If you need HealthKit integration:

1. Enable HealthKit capability in Xcode
2. Add HealthKit usage descriptions to Info.plist
3. Configure HealthKit entitlements

### Push Notifications

For push notifications:

1. Enable Push Notifications capability in Xcode
2. Configure APNs certificates
3. Add notification permissions to the app

### App Store Submission

1. **Archive the app** in Xcode
2. **Upload to App Store Connect**
3. **Configure app metadata**
4. **Submit for review**

## Performance Optimization

### Build Optimization

```bash
# Enable Hermes JavaScript engine (already configured)
# Hermes provides better performance and smaller bundle size

# Optimize images and assets
npx expo optimize

# Analyze bundle size
npx expo bundle-analyzer
```

### Runtime Optimization

- Use React Native performance profiler
- Optimize re-renders with React.memo
- Implement proper state management
- Use lazy loading for heavy components

## Security Considerations

### Medical Data Protection

- All health data should be encrypted at rest and in transit
- Implement proper authentication and authorization
- Follow HIPAA compliance guidelines if applicable
- Use secure storage for sensitive data

### Network Security

- Use HTTPS for all API communications
- Implement certificate pinning for production
- Validate all server responses
- Use secure authentication tokens

## Support and Resources

### Official Documentation

- [Expo iOS Development](https://docs.expo.dev/workflow/ios/)
- [React Native iOS Guide](https://reactnative.dev/docs/running-on-device)
- [iHealth SDK Documentation](https://github.com/iHealthDeviceLabs/iHealth-React-Native-SDK)

### Community Resources

- [Expo Discord](https://discord.gg/expo)
- [React Native Community](https://reactnative.dev/community/overview)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/react-native+ios)

## Testing and Quality Assurance

### Unit Testing

```bash
# Run unit tests
npm test
# or
yarn test

# Run tests with coverage
npm run test:coverage
```

### Integration Testing

```bash
# Test iHealth device integration
# Note: Requires physical devices, won't work in simulator
npm run test:integration
```

### Manual Testing Checklist

#### Authentication Flow
- [ ] User registration works
- [ ] User login works
- [ ] Token refresh works
- [ ] Logout clears all data

#### Device Integration
- [ ] Bluetooth permissions granted
- [ ] iHealth authentication successful
- [ ] Device discovery works
- [ ] Device connection stable
- [ ] Data reading accurate
- [ ] Battery status updates

#### FHIR Integration
- [ ] Data submission to FHIR server
- [ ] Data retrieval from FHIR server
- [ ] Error handling for network issues
- [ ] Offline data storage

### Automated Testing with Detox

```bash
# Install Detox for E2E testing
npm install -g detox-cli
npm install --save-dev detox

# Configure Detox for iOS
detox build --configuration ios.sim.debug
detox test --configuration ios.sim.debug
```

## Deployment Strategies

### Development Deployment

```bash
# Internal testing with TestFlight
npx eas build --platform ios --profile preview
npx eas submit --platform ios --latest
```

### Staging Deployment

```bash
# Staging environment build
npx eas build --platform ios --profile staging
```

### Production Deployment

```bash
# Production build for App Store
npx eas build --platform ios --profile production
npx eas submit --platform ios --latest
```

### Environment Configuration

Create different environment configurations:

```javascript
// app.config.js
export default ({ config }) => {
  const environment = process.env.EXPO_PUBLIC_ENV || 'development';

  return {
    ...config,
    extra: {
      environment,
      apiUrl: environment === 'production'
        ? 'https://api.production.com'
        : 'https://api.staging.com',
    },
  };
};
```

## Monitoring and Analytics

### Crash Reporting

```bash
# Install Sentry for crash reporting
npm install @sentry/react-native

# Configure in app
import * as Sentry from '@sentry/react-native';

Sentry.init({
  dsn: 'YOUR_SENTRY_DSN',
});
```

### Performance Monitoring

```bash
# Install Flipper for debugging
npm install --save-dev react-native-flipper

# Use React Native Performance Monitor
import { enableScreens } from 'react-native-screens';
enableScreens();
```

### Health Data Analytics

- Monitor device connection success rates
- Track data accuracy and completeness
- Analyze user engagement patterns
- Monitor API response times

## Compliance and Regulations

### HIPAA Compliance

- Implement proper data encryption
- Ensure secure data transmission
- Maintain audit logs
- Implement user access controls
- Regular security assessments

### FDA Considerations

- If app is used for medical diagnosis, FDA approval may be required
- Maintain proper documentation
- Implement quality management system
- Regular software validation

### Privacy Regulations

- Implement GDPR compliance if applicable
- Provide clear privacy policy
- Allow users to delete their data
- Implement consent management

## Maintenance and Updates

### Regular Maintenance Tasks

```bash
# Update dependencies monthly
npm update
npm audit fix

# Update iOS deployment target as needed
# Update Xcode and iOS SDK versions
# Review and update permissions
```

### Version Management

```bash
# Semantic versioning
npm version patch  # Bug fixes
npm version minor  # New features
npm version major  # Breaking changes
```

### Backup and Recovery

- Regular database backups
- Source code version control
- Configuration management
- Disaster recovery procedures

---

**Note**: This guide assumes you're working with the FHIR Mobile MVP project structure. Adjust paths and configurations as needed for your specific setup.

For additional support or questions specific to this project, please refer to the project documentation or contact the development team.
