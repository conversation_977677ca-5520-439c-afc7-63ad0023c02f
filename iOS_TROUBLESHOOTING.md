# iOS Troubleshooting Guide

Quick reference for common iOS development issues with the FHIR Mobile MVP project.

## Quick Diagnostics

Run the automated setup verification:
```bash
npm run verify-ios
```

## Quick Fix for iHealth SDK Build Issues

If you're getting architecture or linking errors with the iHealth SDK:
```bash
npm run fix-ios-build
```

## Common Issues & Solutions

### 0. iHealth SDK Architecture Issues ⭐ **MOST COMMON**

#### Error: "building for 'iOS-simulator', but linking in object file built for 'iOS'"
#### Error: "ignoring duplicate libraries: '-lc++'"

**Quick Fix:**
```bash
npm run fix-ios-build
npm run ios
```

**Manual Fix:**
1. Open `ios/Podfile`
2. Replace the `post_install` block with:
```ruby
post_install do |installer|
  react_native_post_install(
    installer,
    config[:reactNativePath],
    :mac_catalyst_enabled => false,
    :ccache_enabled => podfile_properties['apple.ccacheEnabled'] == 'true',
  )

  # Fix for iHealth SDK architecture issues
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      # Exclude arm64 for simulator builds
      config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"

      # Fix duplicate library warnings
      config.build_settings['OTHER_LDFLAGS'] ||= []
      config.build_settings['OTHER_LDFLAGS'] = config.build_settings['OTHER_LDFLAGS'].reject { |flag| flag == '-lc++' }
      config.build_settings['OTHER_LDFLAGS'] << '-lc++' if !config.build_settings['OTHER_LDFLAGS'].include?('-lc++')

      # Ensure minimum iOS version
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
    end
  end
end
```
3. Run: `cd ios && pod install && cd .. && npm run ios`

### 1. Build Errors

#### "No such module 'ExpoModulesCore'"
```bash
cd ios
pod deintegrate
pod install
cd ..
npm run ios
```

#### "Command PhaseScriptExecution failed"
```bash
# Clean build folder
cd ios
rm -rf build/
cd ..
npx expo run:ios --clear
```

#### "Multiple commands produce" error
```bash
# Clean and rebuild
npx expo prebuild --clean
cd ios && pod install && cd ..
```

### 2. CocoaPods Issues

#### "Unable to find a specification for..."
```bash
pod repo update
pod cache clean --all
pod install
```

#### "Permission denied" during pod install
```bash
sudo gem install cocoapods
pod install
```

#### "Xcode version mismatch"
```bash
sudo xcode-select -s /Applications/Xcode.app/Contents/Developer
pod install
```

### 3. Device Connection Issues

#### "Could not find device"
```bash
# List available devices
xcrun simctl list devices

# Or for physical devices
xcrun devicectl list devices
```

#### "Developer Mode not enabled" (iOS 16+)
1. Settings → Privacy & Security → Developer Mode
2. Enable Developer Mode
3. Restart device

#### "Untrusted Developer"
1. Settings → General → VPN & Device Management
2. Find your developer certificate
3. Tap "Trust"

### 4. iHealth SDK Issues

#### "iHealth license not found"
```bash
# Check license file exists
ls -la *.pem
ls -la ios/fhirmobilemvp/*.pem

# Verify plugin is applied
npx expo config --type introspect
```

#### "iHealth authentication failed"
- Ensure license file is for iOS (not Android)
- Verify bundle identifier matches license
- Check license file is not corrupted

#### "Device connection timeout"
- Close iHealth MyVitals app completely
- Reset Bluetooth: Settings → General → Reset → Reset Network Settings
- Restart both device and iPhone

### 5. Bluetooth Permission Issues

#### "Bluetooth permission denied"
```bash
# Verify Info.plist has correct permissions
cat ios/fhirmobilemvp/Info.plist | grep -A 1 "NSBluetooth"
```

#### "Location permission required"
- iOS requires location permission for Bluetooth scanning
- Ensure location permissions are granted in Settings

### 6. Simulator Issues

#### "Simulator not responding"
```bash
# Reset simulator
xcrun simctl shutdown all
xcrun simctl erase all
```

#### "App not installing on simulator"
```bash
# Reset and clean
npx expo run:ios --clear
# Or manually reset simulator content
```

### 7. Xcode Issues

#### "Signing & Capabilities errors"
1. Open `ios/fhirmobilemvp.xcworkspace` in Xcode
2. Select project → Signing & Capabilities
3. Choose your development team
4. Ensure bundle identifier is unique

#### "Provisioning profile errors"
1. Xcode → Preferences → Accounts
2. Download Manual Profiles
3. Or use Automatic signing

### 8. Performance Issues

#### "Metro bundler slow"
```bash
# Clear Metro cache
npx expo start --clear

# Or clear all caches
npm start -- --reset-cache
```

#### "Build taking too long"
```bash
# Enable ccache (if available)
echo 'export CCACHE_DIR="$HOME/.ccache"' >> ~/.zshrc
brew install ccache
```

## Emergency Reset Procedures

### Complete Clean Reset
```bash
# 1. Clean all caches
npm run verify-ios
npx expo start --clear

# 2. Clean iOS build
cd ios
rm -rf build/
pod deintegrate
pod cache clean --all
pod install
cd ..

# 3. Regenerate native code
npx expo prebuild --clean

# 4. Rebuild
npm run ios
```

### Nuclear Option (Last Resort)
```bash
# Delete everything and start fresh
rm -rf node_modules/
rm -rf ios/
rm package-lock.json
npm install
npx expo prebuild
cd ios && pod install && cd ..
npm run ios
```

## Debugging Tools

### Xcode Console
1. Open Xcode
2. Window → Devices and Simulators
3. Select your device
4. View device logs

### React Native Debugger
```bash
# Install React Native Debugger
brew install --cask react-native-debugger

# Enable debugging in app
# Shake device → Debug → Enable Remote JS Debugging
```

### Flipper (Advanced)
```bash
npm install --save-dev react-native-flipper
# Follow Flipper setup guide for detailed debugging
```

## Health Check Commands

```bash
# Verify iOS setup
npm run verify-ios

# Check Xcode installation
xcode-select -p
xcodebuild -version

# Check available simulators
xcrun simctl list devices

# Check connected devices
xcrun devicectl list devices

# Verify CocoaPods
pod --version
pod repo list

# Check Node/npm versions
node --version
npm --version
npx expo --version
```

## Getting Help

### Log Collection
When reporting issues, include:

```bash
# System info
sw_vers
xcodebuild -version
node --version
npm --version

# Project info
cat package.json | grep version
npx expo config --type introspect

# iOS specific
cd ios && pod --version && pod outdated
```

### Common Log Locations
- Xcode logs: `~/Library/Developer/Xcode/DerivedData/`
- iOS Simulator logs: `~/Library/Logs/CoreSimulator/`
- Metro bundler logs: Terminal output
- Device logs: Xcode → Window → Devices and Simulators