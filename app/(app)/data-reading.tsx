/* eslint-disable prettier/prettier */
import React, { useEffect, useRef, useState } from "react";
import {
   ScrollView,
   Pressable,
   Alert,
   View,
   Text,
   ActivityIndicator,
   TextInput,
} from "react-native";
import { useIHealth } from "@/hooks/useIHealth";
import { useRouter, useLocalSearchParams } from "expo-router";
import { getDeviceTypeBySDKType } from "@/constants/deviceTypes";
import { useTheme } from "@/context/ThemeContext";
import { useAuth } from "@/context/AuthContext";
import useApi from "@/hooks/useApi";
import { getDataReadingStyles } from "@/constants/styles";
import { getDataReadingColors } from "@/constants/colors";
// import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";

export default function DataReadingScreen() {
   const router = useRouter();
   const params = useLocalSearchParams();
   const { theme } = useTheme();
   const colors = getDataReadingColors(theme);
   const styles = getDataReadingStyles(colors);
   const { accessToken } = useAuth();
   const { post } = useApi();

   // Get device info from navigation params or connected device
   const deviceMac = params.deviceMac as string;
   const deviceType = params.deviceType as string;
   const deviceName = params.deviceName as string;

   // State for data submission
   const [additionalNotes, setAdditionalNotes] = useState<string>("");
   const [isSubmitting, setIsSubmitting] = useState(false);
   const [lastSubmittedReading, setLastSubmittedReading] = useState<
      string | null
   >(null);

   const {
      connectedDevice,
      connectionStatus,
      // Use generic device data methods for multi-device support
      measurementStatus,
      currentMeasurement,
      measurementHistory,
      batteryLevel,
      batteryLastUpdated,
      startMeasurement,
      stopMeasurement,
      getBatteryLevel,
      getHistoryData,
      clearMeasurementHistory,
      connectDevice,
   } = useIHealth();

   // Battery level logging removed - functionality is working correctly

   // Get device configuration
   const deviceConfig = getDeviceTypeBySDKType(deviceType);

   // Use device info from params if available, otherwise fall back to connected device
   const currentDevice = {
      mac: deviceMac || connectedDevice?.mac,
      type: deviceType || connectedDevice?.type,
      name: deviceName || connectedDevice?.name || `${deviceType} Device`,
   };

   const handleStartMeasurement = () => {
      if (!currentDevice.mac || !currentDevice.type) {
         Alert.alert(
            "No Device Information",
            "Device information is missing. Please go back and select a device.",
         );
         return;
      }

      if (connectionStatus !== "connected") {
         Alert.alert(
            "Device Not Ready",
            "Please ensure the device is connected before starting measurement.",
         );
         return;
      }

      startMeasurement(currentDevice.mac, currentDevice.type);
   };

   const handleStopMeasurement = () => {
      if (!currentDevice.type) {
         return;
      }
      stopMeasurement(currentDevice.type);
   };

   const handleGetHistory = () => {
      if (!currentDevice.mac || !currentDevice.type) {
         Alert.alert(
            "No Device Information",
            "Device information is missing. Please go back and select a device.",
         );
         return;
      }

      getHistoryData(currentDevice.mac, currentDevice.type);
   };

   // Use ref to track if battery has been fetched for current device to prevent infinite loop
   const batteryFetchedRef = useRef<string | null>(null);

   useEffect(() => {
      connectDevice({ mac: deviceMac, type: deviceType });
   }, []);

   useEffect(() => {
      const deviceKey = `${connectedDevice?.mac}-${connectedDevice?.type}`;

      if (
         connectionStatus === "connected" &&
         connectedDevice?.mac &&
         connectedDevice?.type &&
         batteryFetchedRef.current !== deviceKey
      ) {
         // Mark as fetched immediately to prevent duplicate requests
         batteryFetchedRef.current = deviceKey;

         // Call battery level immediately
         getBatteryLevel(connectedDevice.mac, connectedDevice.type);
      } else if (connectionStatus === "disconnected") {
         Alert.alert(
            "Connection lost",
            "The connection to your device was lost. Please try again by reconnecting.",
            [{ text: "OK", onPress: () => router.replace("/scan") }],
         );
         batteryFetchedRef.current = null; // Reset when disconnected
      }
   }, [
      connectionStatus,
      connectedDevice?.mac,
      connectedDevice?.type,
      getBatteryLevel,
   ]);

   const getMeasurementStatusText = () => {
      switch (measurementStatus) {
         case "measuring":
            return "Measuring...";
         case "completed":
            return "Measurement Complete";
         case "failed":
            return "Measurement Failed";
         case "timeout":
            return "Measurement Timeout";
         default:
            return "Ready to Measure";
      }
   };

   // Data parsing function for pulse oximeter readings
   const parseDeviceDataForAPI = () => {
      if (!currentMeasurement) {
         return null;
      }

      // For pulse oximeter (PO3) data
      if (currentMeasurement.spo2 !== undefined) {
         return {
            bloodOxygen: currentMeasurement.spo2,
            pi: parseFloat((currentMeasurement.pi || 0).toFixed(2)),
            pulseRate:
               currentMeasurement.pulseRate || currentMeasurement.heartRate,
            timestamp: new Date().toISOString(),
         };
      }

      // For scale (HS2S) data - future enhancement
      if (currentMeasurement.weight !== undefined) {
         return {
            weight: currentMeasurement.weight,
            // unit: currentMeasurement.unit || "kg",
            // bmi: currentMeasurement.bmi,
            timestamp: new Date().toISOString(),
         };
      }

      return null;
   };

   // Enhanced tag metadata for medical device readings
   const generateDeviceTags = () => {
      const tags: Record<string, string> = {
         "device type": deviceConfig?.name || "Unknown Device",
      };

      if (connectedDevice) {
         tags["device model"] = connectedDevice.name || "Unknown Model";
         tags["device mac"] = connectedDevice.mac.slice(-6); // Last 6 characters for privacy
         tags["connection type"] = "Bluetooth";
      }

      if (currentMeasurement) {
         if (currentMeasurement.spo2 !== undefined) {
            tags["measurement type"] = "Pulse Oximetry";
            tags["device category"] = "Pulse Oximeter";
         } else if (currentMeasurement.weight !== undefined) {
            tags["measurement type"] = "Body Composition";
            tags["device category"] = "Smart Scale";
         }
      }

      if (batteryLevel !== null) {
         tags["battery level"] = `${batteryLevel}%`;
      }

      // Add measurement conditions
      tags["measurement environment"] = "Home";
      tags["data source"] = "iHealth SDK";
      tags["app version"] = "1.0.0"; // Could be dynamic from app.json

      return tags;
   };

   // API submission function
   const submitReadingToAPI = async () => {
      if (!currentMeasurement || !accessToken) {
         Alert.alert(
            "Cannot Submit",
            "No measurement data available or user not authenticated.",
         );
         return;
      }

      // Check if this reading was already submitted
      const readingId = `${currentMeasurement.timestamp}-${currentMeasurement.deviceMac}`;
      if (lastSubmittedReading === readingId) {
         Alert.alert(
            "Already Submitted",
            "This reading has already been submitted to the server.",
         );
         return;
      }

      const deviceReading = parseDeviceDataForAPI();
      if (!deviceReading) {
         Alert.alert(
            "Invalid Data",
            "Unable to parse device data for submission.",
         );
         return;
      }

      setIsSubmitting(true);

      try {
         const readingPayload = {
            details: {
               deviceReading,
               tags: generateDeviceTags(),
               additionalNotes:
                  additionalNotes.trim() || "No additional notes provided",
            },
         };

         console.log(readingPayload);

         await post("/readings", {
            data: readingPayload,
            token: accessToken,
         });

         // Mark this reading as submitted
         setLastSubmittedReading(readingId);

         Alert.alert(
            "Success",
            "Reading submitted successfully to the server!",
            [
               {
                  text: "OK",
                  style: "default",
               },
            ],
         );

         // Clear notes after successful submission
         setAdditionalNotes("");
      } catch (error) {
         console.error("Failed to submit reading:", error);
         Alert.alert(
            "Submission Failed",
            error instanceof Error
               ? error.message
               : "An unexpected error occurred while submitting the reading.",
         );
      } finally {
         setIsSubmitting(false);
      }
   };

   return (
      <ScrollView
         style={styles.scrollView}
         contentContainerStyle={styles.scrollViewContent}
         showsVerticalScrollIndicator={false}
      >
         {/* Clean Header */}
         <View style={styles.headerContainer}>
            <View style={styles.headerRow}>
               <Pressable
                  onPress={() => router.back()}
                  style={styles.backButton}
               >
                  <Ionicons
                     name="arrow-back"
                     size={24}
                     color={colors.textPrimary}
                  />
               </Pressable>
               <Text style={styles.headerTitle}>
                  {deviceConfig?.name || "Pulse Oximeter"} Air Monitor
               </Text>
               <Text style={styles.headerIcon}>🫁</Text>
            </View>
         </View>

         <View style={styles.mainContainer}>
            {/* Device Connection Status */}
            <View style={styles.deviceStatusCard}>
               <View style={styles.statusIcon}>
                  <Ionicons name="checkmark" size={20} color="white" />
               </View>
               <View style={styles.deviceStatusTextContainer}>
                  <Text style={styles.deviceStatusTitle}>Device Connected</Text>
                  <Text style={styles.deviceStatusSubtitle}>
                     • {connectedDevice?.mac?.slice(-6) || "7C23FF"}
                  </Text>
               </View>
            </View>

            {/* Measurement Status with Embedded Cards */}
            <View style={styles.measurementStatusCard}>
               {/* Status Header */}
               <View style={styles.statusHeader}>
                  <View
                     style={[
                        styles.measurementStatusIcon,
                        {
                           backgroundColor:
                              measurementStatus === "completed"
                                 ? colors.success
                                 : colors.textSecondary,
                        },
                     ]}
                  >
                     {measurementStatus === "measuring" ? (
                        <ActivityIndicator size="small" color="white" />
                     ) : (
                        <Ionicons
                           name={
                              measurementStatus === "completed"
                                 ? "checkmark"
                                 : measurementStatus === "failed"
                                   ? "close"
                                   : "pulse"
                           }
                           size={20}
                           color="white"
                        />
                     )}
                  </View>
                  <Text style={styles.statusTitle}>
                     {getMeasurementStatusText()}
                  </Text>
               </View>

               {/* Embedded Measurement Cards */}
               {currentMeasurement && measurementStatus === "completed" && (
                  <View style={styles.measurementCardsContainer}>
                     {/* First Row: SpO2 and Heart Rate */}
                     <View style={styles.measurementRow}>
                        <View style={styles.measurementCardWrapper}>
                           <View
                              style={[
                                 styles.measurementCard,
                                 { backgroundColor: colors.spo2Card },
                              ]}
                           >
                              <Text
                                 style={[
                                    styles.measurementCardLabel,
                                    { color: colors.spo2Text },
                                 ]}
                              >
                                 SpO2
                              </Text>
                              <Text
                                 style={[
                                    styles.measurementCardValue,
                                    { color: colors.spo2Text },
                                 ]}
                              >
                                 {currentMeasurement.spo2}%
                              </Text>
                              <Text
                                 style={[
                                    styles.measurementCardUnitHidden,
                                    { color: colors.spo2Text },
                                 ]}
                              >
                                 {" "}
                              </Text>
                           </View>
                        </View>
                        <View style={styles.measurementCardWrapper}>
                           <View
                              style={[
                                 styles.measurementCard,
                                 { backgroundColor: colors.heartRateCard },
                              ]}
                           >
                              <Text
                                 style={[
                                    styles.measurementCardLabel,
                                    { color: colors.heartRateText },
                                 ]}
                              >
                                 Heart Rate
                              </Text>
                              <Text
                                 style={[
                                    styles.measurementCardValueCentered,
                                    { color: colors.heartRateText },
                                 ]}
                              >
                                 {currentMeasurement.heartRate}
                              </Text>
                              <Text
                                 style={[
                                    styles.measurementCardUnit,
                                    { color: colors.heartRateText },
                                 ]}
                              >
                                 BPM
                              </Text>
                           </View>
                        </View>
                     </View>

                     {/* Second Row: Pulse Rate and PI */}
                     <View style={styles.measurementRowSecond}>
                        <View style={styles.measurementCardWrapper}>
                           <View
                              style={[
                                 styles.measurementCard,
                                 { backgroundColor: colors.pulseRateCard },
                              ]}
                           >
                              <Text
                                 style={[
                                    styles.measurementCardLabel,
                                    { color: colors.pulseRateText },
                                 ]}
                              >
                                 Pulse Rate
                              </Text>
                              <Text
                                 style={[
                                    styles.measurementCardValueCentered,
                                    { color: colors.pulseRateText },
                                 ]}
                              >
                                 {currentMeasurement.pulseRate}
                              </Text>
                              <Text
                                 style={[
                                    styles.measurementCardUnit,
                                    { color: colors.pulseRateText },
                                 ]}
                              >
                                 BPM
                              </Text>
                           </View>
                        </View>
                        <View style={styles.measurementCardWrapper}>
                           <View
                              style={[
                                 styles.measurementCard,
                                 { backgroundColor: colors.piCard },
                              ]}
                           >
                              <Text
                                 style={[
                                    styles.measurementCardLabel,
                                    { color: colors.piText },
                                 ]}
                              >
                                 PI
                              </Text>
                              <Text
                                 style={[
                                    styles.measurementCardValue,
                                    { color: colors.piText },
                                 ]}
                              >
                                 {parseFloat(
                                    (currentMeasurement.pi || 0).toString(),
                                 ).toFixed(2)}
                              </Text>
                              <Text
                                 style={[
                                    styles.measurementCardUnitHidden,
                                    { color: colors.piText },
                                 ]}
                              >
                                 {" "}
                              </Text>
                           </View>
                        </View>
                     </View>
                  </View>
               )}
            </View>

            {/* Timestamp */}
            {currentMeasurement && (
               <View style={styles.timestampCard}>
                  <Text style={styles.timestampIcon}>📅</Text>
                  <Text style={styles.timestampText}>
                     {new Date(currentMeasurement.timestamp).toLocaleDateString(
                        "en-US",
                        {
                           month: "numeric",
                           day: "numeric",
                           year: "numeric",
                        },
                     )}
                     {", "}
                     {new Date(currentMeasurement.timestamp).toLocaleTimeString(
                        "en-US",
                        {
                           hour: "numeric",
                           minute: "2-digit",
                           second: "2-digit",
                           hour12: true,
                        },
                     )}
                  </Text>
               </View>
            )}

            {/* Data Submission Section */}
            {currentMeasurement && measurementStatus === "completed" && (
               <View style={styles.dataSubmissionCard}>
                  <View style={styles.dataSubmissionHeader}>
                     <View style={styles.dataSubmissionIcon}>
                        <Ionicons name="cloud-upload" size={20} color="white" />
                     </View>
                     <Text style={[styles.dataSubmissionTitle, styles.flex1]}>
                        Submit Reading
                     </Text>
                  </View>

                  {/* Notes Input */}
                  <View style={styles.notesInputContainer}>
                     <Text style={styles.notesLabel}>
                        Additional Notes (Optional)
                     </Text>
                     <TextInput
                        style={styles.notesInput}
                        placeholder="Add any notes about this reading"
                        placeholderTextColor={colors.textSecondary}
                        value={additionalNotes}
                        onChangeText={setAdditionalNotes}
                        multiline
                        numberOfLines={3}
                        maxLength={500}
                     />
                     <Text
                        style={[
                           styles.notesHelper,
                           { textAlign: "right", marginTop: 4 },
                        ]}
                     >
                        {additionalNotes.length}/500
                     </Text>
                  </View>

                  {/* Submit Button */}
                  <Pressable
                     style={({ pressed }) => [
                        styles.submitButtonContainer,
                        lastSubmittedReading ===
                        `${currentMeasurement.timestamp}-${currentMeasurement.deviceMac}`
                           ? styles.submitButtonContainerSuccess
                           : null,
                        {
                           shadowOffset: { width: 0, height: 4 },
                           shadowOpacity: 0.2,
                           elevation: 5,
                           opacity: pressed ? 0.8 : isSubmitting ? 0.6 : 1,
                        },
                     ]}
                     onPress={submitReadingToAPI}
                     disabled={isSubmitting}
                  >
                     {isSubmitting ? (
                        <ActivityIndicator
                           size="small"
                           color="white"
                           style={styles.iconMarginRight8}
                        />
                     ) : (
                        <Ionicons
                           name={
                              lastSubmittedReading ===
                              `${currentMeasurement.timestamp}-${currentMeasurement.deviceMac}`
                                 ? "checkmark-circle"
                                 : "cloud-upload"
                           }
                           size={20}
                           color="white"
                           style={styles.iconMarginRight8}
                        />
                     )}
                     <Text style={[styles.submitButtonText, { fontWeight: "bold" }]}>
                        {isSubmitting
                           ? "Submitting..."
                           : lastSubmittedReading ===
                               `${currentMeasurement.timestamp}-${currentMeasurement.deviceMac}`
                             ? "Reading Submitted"
                             : "Submit to Server"}
                     </Text>
                  </Pressable>

                  {/* Submission Status */}
                  {lastSubmittedReading ===
                     `${currentMeasurement.timestamp}-${currentMeasurement.deviceMac}` && (
                     <View
                        style={[
                           styles.successIndicatorContainer,
                           {
                              padding: 12,
                              backgroundColor: colors.success + "20",
                              borderRadius: 12,
                           },
                        ]}
                     >
                        <Ionicons
                           name="checkmark-circle"
                           size={16}
                           color={colors.success}
                        />
                        <Text style={styles.successIndicatorText}>
                           Successfully submitted to server
                        </Text>
                     </View>
                  )}
               </View>
            )}

            {/* Battery Status */}
            {batteryLevel !== null && (
               <View style={styles.batteryCard}>
                  <View style={styles.batteryIconContainer}>
                     <Ionicons
                        name="battery-charging"
                        size={24}
                        color="white"
                     />
                  </View>
                  <View style={styles.batteryTextContainer}>
                     <Text style={styles.batteryTitle}>
                        Battery Level
                     </Text>
                     <Text style={[styles.batteryLevel, { color: colors.primary }]}>
                        {batteryLevel}%
                     </Text>
                     {batteryLastUpdated && (
                        <Text style={[styles.batteryLastUpdated, { marginTop: 2 }]}>
                           Updated:{" "}
                           {new Date(batteryLastUpdated).toLocaleTimeString()}
                        </Text>
                     )}
                  </View>
               </View>
            )}

            {/* Control Buttons */}
            <View style={styles.controlButtonsContainer}>
               {/* Primary Action Button */}
               <Pressable
                  style={({ pressed }) => [
                     styles.primaryActionButton,
                     {
                        backgroundColor:
                           measurementStatus === "measuring"
                              ? colors.error
                              : colors.primary,
                        padding: 18,
                        opacity: pressed ? 0.8 : !connectedDevice ? 0.5 : 1,
                     },
                  ]}
                  onPress={
                     measurementStatus === "measuring"
                        ? handleStopMeasurement
                        : handleStartMeasurement
                  }
                  disabled={!connectedDevice}
               >
                  <Ionicons
                     name={measurementStatus === "measuring" ? "stop" : "play"}
                     size={20}
                     color="white"
                     style={styles.iconMarginRight8}
                  />
                  <Text style={[styles.primaryActionButtonText, { fontWeight: "bold" }]}>
                     {measurementStatus === "measuring"
                        ? "Stop Measurement"
                        : "Start Measurement"}
                  </Text>
               </Pressable>

               {/* Secondary Action Button */}
               <Pressable
                  style={({ pressed }) => [
                     styles.secondaryActionButton,
                     {
                        opacity: pressed ? 0.8 : !connectedDevice ? 0.5 : 1,
                        borderWidth: 1,
                        borderColor: colors.border,
                     },
                  ]}
                  onPress={handleGetHistory}
                  disabled={!connectedDevice}
               >
                  <Ionicons
                     name="time"
                     size={20}
                     color={colors.textPrimary}
                     style={styles.iconMarginRight8}
                  />
                  <Text style={styles.secondaryActionButtonText}>
                     Get History Data
                  </Text>
               </Pressable>
            </View>

            {/* Measurement History */}
            {measurementHistory.length > 0 && (
               <View style={styles.historyCard}>
                  <View style={styles.historyHeader}>
                     <Text style={styles.historyTitle}>
                        Recent Measurements ({measurementHistory.length})
                     </Text>
                     <Pressable
                        style={({ pressed }) => [
                           styles.historyBadgeContainer,
                           {
                              backgroundColor: colors.textSecondary,
                              paddingVertical: 6,
                              borderRadius: 8,
                              opacity: pressed ? 0.8 : 1,
                           },
                        ]}
                        onPress={clearMeasurementHistory}
                     >
                        <Text style={styles.historyBadge}>
                           Clear All
                        </Text>
                     </Pressable>
                  </View>

                  {measurementHistory.slice(0, 5).map((measurement, index) => (
                     <View
                        key={index}
                        style={[
                           styles.historyItemContainer,
                           {
                              borderLeftWidth: 4,
                              borderLeftColor: colors.primary,
                           },
                        ]}
                     >
                        <View
                           style={[
                              styles.historyItemRow,
                              { alignItems: "flex-start" },
                           ]}
                        >
                           <Text style={styles.historyItemValueText}>
                              SpO2: {measurement.spo2}%
                           </Text>
                           <Text
                              style={[
                                 styles.historyItemValueText,
                                 { color: colors.secondary },
                              ]}
                           >
                              HR: {measurement.heartRate} BPM
                           </Text>
                        </View>
                        <Text style={styles.historyItemTimestampText}>
                           📅 {new Date(measurement.timestamp).toLocaleString()}
                        </Text>
                     </View>
                  ))}
               </View>
            )}
         </View>
      </ScrollView>
   );
}
