{"name": "fhir-mobile-mvp", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web --port 5000", "lint": "expo lint", "postinstall": "patch-package", "verify-ios": "node ./scripts/verify-ios-setup.js", "setup-ios": "npm run verify-ios && npx expo prebuild --platform ios && cd ios && pod install"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.6", "@ihealth/ihealthlibrary-react-native": "^1.8.0", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "dotenv": "^16.5.0", "expo": "53.0.19", "expo-blur": "~14.1.5", "expo-constants": "~17.1.5", "expo-dev-client": "~5.2.4", "expo-font": "~13.3.0", "expo-haptics": "~14.1.4", "expo-image": "~2.3.2", "expo-linking": "~7.1.7", "expo-router": "~5.1.3", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "lucide-react-native": "^0.525.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-ble-plx": "^3.5.0", "react-native-edge-to-edge": "1.6.0", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "expo-linear-gradient": "~14.1.5"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "@types/react-native-vector-icons": "^6.4.18", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^9.27.0", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "patch-package": "^8.0.0", "prettier": "^3.5.3", "typescript": "~5.8.3"}, "private": true}