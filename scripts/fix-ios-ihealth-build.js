#!/usr/bin/env node

/**
 * iOS iHealth SDK Build Fix Script
 *
 * This script fixes the common iOS build issues with the iHealth SDK:
 * - Architecture mismatch between simulator and device builds
 * - Duplicate library linking errors
 * - Hermes configuration warnings
 */

const fs = require("fs");
const path = require("path");
const { execSync } = require("child_process");

// ANSI color codes for console output
const colors = {
   green: "\x1b[32m",
   red: "\x1b[31m",
   yellow: "\x1b[33m",
   blue: "\x1b[34m",
   reset: "\x1b[0m",
   bold: "\x1b[1m",
};

const log = (message, color = colors.reset) => {
   console.log(`${color}${message}${colors.reset}`);
};

const success = (message) => log(`✅ ${message}`, colors.green);
const error = (message) => log(`❌ ${message}`, colors.red);
const warning = (message) => log(`⚠️  ${message}`, colors.yellow);
const info = (message) => log(`ℹ️  ${message}`, colors.blue);

const fixPodfile = () => {
   log("\n🔧 Fixing Podfile for iHealth SDK compatibility...", colors.bold);

   const podfilePath = path.join(process.cwd(), "ios", "Podfile");

   if (!fs.existsSync(podfilePath)) {
      error(
         "Podfile not found. Make sure you are in the project root and iOS folder exists.",
      );
      return false;
   }

   let podfileContent = fs.readFileSync(podfilePath, "utf8");

   // Check if our fixes are already applied
   if (podfileContent.includes("# iHealth SDK architecture fix")) {
      success("iHealth SDK fixes already applied to Podfile");
      return true;
   }

   // Create the fix content
   const iHealthFix = `
  # iHealth SDK architecture fix for simulator compatibility
  post_install do |installer|
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      :ccache_enabled => podfile_properties['apple.ccacheEnabled'] == 'true',
    )

    # This is necessary for Xcode 14, because it signs resource bundles by default
    # when building for devices.
    installer.target_installation_results.pod_target_installation_results
      .each do |pod_name, target_installation_result|
      target_installation_result.resource_bundle_targets.each do |resource_bundle_target|
        resource_bundle_target.build_configurations.each do |config|
          config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
        end
      end
    end

    # Fix for iHealth SDK architecture issues
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        # Exclude arm64 for simulator builds to fix iHealth SDK linking
        config.build_settings["EXCLUDED_ARCHS[sdk=iphonesimulator*]"] = "arm64"

        # Fix duplicate library warnings - handle both string and array cases
        other_ldflags = config.build_settings['OTHER_LDFLAGS'] || []
        other_ldflags = [other_ldflags] if other_ldflags.is_a?(String)
        other_ldflags = other_ldflags.reject { |flag| flag == '-lc++' }
        other_ldflags << '-lc++' unless other_ldflags.include?('-lc++')
        config.build_settings['OTHER_LDFLAGS'] = other_ldflags

        # Ensure minimum iOS version
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
      end
    end
  end`;

   try {
      // Replace existing post_install block or add new one
      if (podfileContent.includes("post_install do |installer|")) {
         // Replace existing post_install block
         podfileContent = podfileContent.replace(
            /post_install do \|installer\|[\s\S]*?^  end$/m,
            iHealthFix.trim(),
         );
      } else {
         // Add new post_install block before the final 'end'
         podfileContent = podfileContent.replace(
            /^end\s*$/m,
            `${iHealthFix}\nend`,
         );
      }

      // Write the updated Podfile
      fs.writeFileSync(podfilePath, podfileContent);
      success("Podfile updated with iHealth SDK fixes");
      return true;
   } catch (err) {
      error(`Failed to update Podfile: ${err.message}`);
      return false;
   }
};

const cleanAndReinstallPods = () => {
   log("\n🧹 Cleaning and reinstalling CocoaPods...", colors.bold);

   try {
      // Change to iOS directory
      process.chdir("ios");

      // Clean existing pods
      if (fs.existsSync("Podfile.lock")) {
         execSync("pod deintegrate", { stdio: "inherit" });
         success("Deintegrated existing pods");
      }

      // Clear pod cache
      execSync("pod cache clean --all", { stdio: "inherit" });
      success("Cleared pod cache");

      // Install pods
      execSync("pod install", { stdio: "inherit" });
      success("Reinstalled pods with fixes");

      // Change back to project root
      process.chdir("..");

      return true;
   } catch (err) {
      error(`Failed to reinstall pods: ${err.message}`);
      process.chdir("..");
      return false;
   }
};

const cleanXcodeBuild = () => {
   log("\n🗑️  Cleaning Xcode build cache...", colors.bold);

   try {
      const buildPath = path.join("ios", "build");
      if (fs.existsSync(buildPath)) {
         fs.rmSync(buildPath, { recursive: true, force: true });
         success("Cleaned Xcode build directory");
      }

      // Clean derived data
      execSync("rm -rf ~/Library/Developer/Xcode/DerivedData/fhirmobilemvp-*", {
         stdio: "pipe",
      });
      success("Cleaned Xcode derived data");

      return true;
   } catch (err) {
      warning(`Could not clean all build caches: ${err.message}`);
      return true; // Non-critical error
   }
};

const main = () => {
   log("🔧 iOS iHealth SDK Build Fix", colors.blue + colors.bold);
   log("================================", colors.blue);

   info("This script will fix common iOS build issues with the iHealth SDK:");
   info("• Architecture mismatch for simulator builds");
   info("• Duplicate library linking errors");
   info("• Build configuration issues");

   let success_count = 0;
   const total_steps = 3;

   // Step 1: Fix Podfile
   if (fixPodfile()) success_count++;

   // Step 2: Clean and reinstall pods
   if (cleanAndReinstallPods()) success_count++;

   // Step 3: Clean Xcode build cache
   if (cleanXcodeBuild()) success_count++;

   log("\n📊 Summary", colors.bold);
   if (success_count === total_steps) {
      success(
         `All fixes applied successfully! (${success_count}/${total_steps})`,
      );
      log("\n🚀 Next steps:", colors.green + colors.bold);
      info("1. Try building again: npm run ios");
      info("2. If building for simulator, make sure to select iOS Simulator");
      info(
         "3. If building for device, make sure device is connected and trusted",
      );
   } else {
      warning(`${success_count}/${total_steps} fixes applied`);
      log("\n🔧 Manual steps may be required:", colors.yellow + colors.bold);
      info("1. Check the error messages above");
      info("2. Ensure you have the latest Xcode version");
      info("3. Try running: npx expo run:ios --clear");
   }

   log("\n💡 If you still have issues:", colors.bold);
   info("• Check iOS_TROUBLESHOOTING.md for more solutions");
   info("• Try building for a different target (simulator vs device)");
   info("• Ensure your iHealth license file is in the correct location");
};

// Run the fix
if (require.main === module) {
   main();
}

module.exports = { main };
