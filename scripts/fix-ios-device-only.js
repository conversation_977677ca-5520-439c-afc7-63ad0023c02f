#!/usr/bin/env node

/**
 * iOS Device-Only Build Fix Script
 * 
 * This script configures the iOS build to work reliably with physical devices only.
 * The iHealth SDK has compatibility issues with iOS simulator, so this focuses on
 * device builds which are required for actual iHealth device testing anyway.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// ANSI color codes for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

const log = (message, color = colors.reset) => {
  console.log(`${color}${message}${colors.reset}`);
};

const success = (message) => log(`✅ ${message}`, colors.green);
const error = (message) => log(`❌ ${message}`, colors.red);
const warning = (message) => log(`⚠️  ${message}`, colors.yellow);
const info = (message) => log(`ℹ️  ${message}`, colors.blue);

const fixPodfileForDeviceOnly = () => {
  log('\n🔧 Configuring Podfile for device-only builds...', colors.bold);
  
  const podfilePath = path.join(process.cwd(), 'ios', 'Podfile');
  
  if (!fs.existsSync(podfilePath)) {
    error('Podfile not found. Make sure you are in the project root and iOS folder exists.');
    return false;
  }
  
  let podfileContent = fs.readFileSync(podfilePath, 'utf8');
  
  // Check if our fixes are already applied
  if (podfileContent.includes('# iHealth SDK device-only configuration')) {
    success('Device-only configuration already applied to Podfile');
    return true;
  }
  
  // Create the simplified device-only fix
  const deviceOnlyFix = `
  # iHealth SDK device-only configuration
  post_install do |installer|
    react_native_post_install(
      installer,
      config[:reactNativePath],
      :mac_catalyst_enabled => false,
      :ccache_enabled => podfile_properties['apple.ccacheEnabled'] == 'true',
    )

    # This is necessary for Xcode 14, because it signs resource bundles by default
    # when building for devices.
    installer.target_installation_results.pod_target_installation_results
      .each do |pod_name, target_installation_result|
      target_installation_result.resource_bundle_targets.each do |resource_bundle_target|
        resource_bundle_target.build_configurations.each do |config|
          config.build_settings['CODE_SIGNING_ALLOWED'] = 'NO'
        end
      end
    end

    # Configure for device builds only (iHealth SDK works best on physical devices)
    installer.pods_project.targets.each do |target|
      target.build_configurations.each do |config|
        # Ensure minimum iOS version for device compatibility
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
        
        # Optimize for device builds
        config.build_settings['ONLY_ACTIVE_ARCH'] = 'NO'
        config.build_settings['VALID_ARCHS'] = 'arm64'
        
        # Remove problematic simulator architectures
        config.build_settings.delete('EXCLUDED_ARCHS[sdk=iphonesimulator*]')
      end
    end
  end`;

  try {
    // Replace existing post_install block or add new one
    if (podfileContent.includes('post_install do |installer|')) {
      // Replace existing post_install block
      podfileContent = podfileContent.replace(
        /post_install do \|installer\|[\s\S]*?^  end$/m,
        deviceOnlyFix.trim()
      );
    } else {
      // Add new post_install block before the final 'end'
      podfileContent = podfileContent.replace(/^end\s*$/m, `${deviceOnlyFix}\nend`);
    }
    
    // Write the updated Podfile
    fs.writeFileSync(podfilePath, podfileContent);
    success('Podfile configured for device-only builds');
    return true;
  } catch (err) {
    error(`Failed to update Podfile: ${err.message}`);
    return false;
  }
};

const cleanAndReinstallPods = () => {
  log('\n🧹 Cleaning and reinstalling CocoaPods...', colors.bold);
  
  try {
    // Change to iOS directory
    process.chdir('ios');
    
    // Clean existing pods
    if (fs.existsSync('Podfile.lock')) {
      execSync('pod deintegrate', { stdio: 'inherit' });
      success('Deintegrated existing pods');
    }
    
    // Clear pod cache
    execSync('pod cache clean --all', { stdio: 'inherit' });
    success('Cleared pod cache');
    
    // Install pods
    execSync('pod install', { stdio: 'inherit' });
    success('Reinstalled pods for device builds');
    
    // Change back to project root
    process.chdir('..');
    
    return true;
  } catch (err) {
    error(`Failed to reinstall pods: ${err.message}`);
    process.chdir('..');
    return false;
  }
};

const cleanXcodeBuild = () => {
  log('\n🗑️  Cleaning Xcode build cache...', colors.bold);
  
  try {
    const buildPath = path.join('ios', 'build');
    if (fs.existsSync(buildPath)) {
      fs.rmSync(buildPath, { recursive: true, force: true });
      success('Cleaned Xcode build directory');
    }
    
    // Clean derived data
    execSync('rm -rf ~/Library/Developer/Xcode/DerivedData/fhirmobilemvp-*', { stdio: 'pipe' });
    success('Cleaned Xcode derived data');
    
    return true;
  } catch (err) {
    warning(`Could not clean all build caches: ${err.message}`);
    return true; // Non-critical error
  }
};

const main = () => {
  log('📱 iOS Device-Only Build Configuration', colors.blue + colors.bold);
  log('=====================================', colors.blue);
  
  warning('IMPORTANT: This configures iOS builds for physical devices only.');
  info('The iHealth SDK has compatibility issues with iOS simulator.');
  info('For iHealth device testing, you need a physical iOS device anyway.');
  
  let success_count = 0;
  const total_steps = 3;
  
  // Step 1: Fix Podfile for device-only builds
  if (fixPodfileForDeviceOnly()) success_count++;
  
  // Step 2: Clean and reinstall pods
  if (cleanAndReinstallPods()) success_count++;
  
  // Step 3: Clean Xcode build cache
  if (cleanXcodeBuild()) success_count++;
  
  log('\n📊 Summary', colors.bold);
  if (success_count === total_steps) {
    success(`Device-only configuration applied successfully! (${success_count}/${total_steps})`);
    log('\n🚀 Next steps:', colors.green + colors.bold);
    info('1. Connect your physical iOS device');
    info('2. Trust the device in Xcode (Window → Devices and Simulators)');
    info('3. Build for device: npx expo run:ios --device');
    info('4. Test iHealth device connectivity on the physical device');
    
    log('\n⚠️  Important Notes:', colors.yellow + colors.bold);
    warning('• Simulator builds may not work reliably with iHealth SDK');
    warning('• Always test iHealth features on physical devices');
    warning('• Bluetooth and iHealth devices require physical hardware');
  } else {
    warning(`${success_count}/${total_steps} steps completed`);
    log('\n🔧 Manual steps may be required:', colors.yellow + colors.bold);
    info('1. Check the error messages above');
    info('2. Ensure you have the latest Xcode version');
    info('3. Try running: npx expo run:ios --device --clear');
  }
  
  log('\n💡 For troubleshooting:', colors.bold);
  info('• Check iOS_TROUBLESHOOTING.md');
  info('• Ensure your iOS device is connected and trusted');
  info('• Verify your Apple Developer account is set up');
};

// Run the fix
if (require.main === module) {
  main();
}

module.exports = { main };
